/* Dark Mode Style Sheet for Damaged Inventory Management System */

QMainWindow, QDialog, QWidget {
    background-color: #2D2D30;
    color: #FFFFFF;
    font-size: 11pt;
}

QMenuBar {
    background-color: #1E1E1E;
    color: #FFFFFF;
}

QMenuBar::item {
    background-color: #1E1E1E;
    color: #FFFFFF;
}

QMenuBar::item:selected {
    background-color: #3E3E40;
}

QMenu {
    background-color: #1E1E1E;
    color: #FFFFFF;
    border: 1px solid #3E3E40;
}

QMenu::item:selected {
    background-color: #3E3E40;
}

QPushButton {
    background-color: #0078D7;
    color: white;
    border: none;
    padding: 5px 15px;
    border-radius: 3px;
}

QPushButton:hover {
    background-color: #1C97EA;
}

QPushButton:pressed {
    background-color: #00559B;
}

QPushButton:disabled {
    background-color: #3E3E40;
    color: #9D9D9D;
}

QLineEdit, QTextEdit, QComboBox, QDateEdit, QSpinBox, QDoubleSpinBox {
    background-color: #3E3E40;
    color: #FFFFFF;
    border: 1px solid #555555;
    border-radius: 3px;
    padding: 3px;
}

QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QDateEdit:focus {
    border: 1px solid #0078D7;
}

QComboBox::drop-down {
    border: 0px;
    background-color: #0078D7;
}

QComboBox QAbstractItemView {
    background-color: #3E3E40;
    color: #FFFFFF;
    selection-background-color: #0078D7;
}

QTableView {
    background-color: #2D2D30;
    color: #FFFFFF;
    gridline-color: #3E3E40;
    selection-background-color: #0078D7;
    selection-color: #FFFFFF;
    alternate-background-color: #3E3E40;
}

QHeaderView::section {
    background-color: #1E1E1E;
    color: #FFFFFF;
    padding: 5px;
    border: 1px solid #3E3E40;
}

QTabWidget::pane {
    border: 1px solid #3E3E40;
}

QTabBar::tab {
    background-color: #2D2D30;
    color: #FFFFFF;
    padding: 8px 12px;
    border: 1px solid #3E3E40;
    border-bottom: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

QTabBar::tab:selected {
    background-color: #0078D7;
}

QTabBar::tab:!selected {
    margin-top: 2px;
}

QLabel {
    color: #FFFFFF;
}

QGroupBox {
    border: 1px solid #3E3E40;
    border-radius: 5px;
    margin-top: 10px;
    font-weight: bold;
    color: #FFFFFF;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    left: 10px;
    padding: 0 5px;
}

QStatusBar {
    background-color: #1E1E1E;
    color: #FFFFFF;
}

QScrollBar:vertical {
    border: none;
    background-color: #2D2D30;
    width: 10px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #5E5E5E;
    min-height: 20px;
    border-radius: 5px;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: none;
    background-color: #2D2D30;
    height: 10px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #5E5E5E;
    min-width: 20px;
    border-radius: 5px;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

QCheckBox {
    color: #FFFFFF;
}

QCheckBox::indicator {
    width: 13px;
    height: 13px;
}

QCheckBox::indicator:unchecked {
    border: 1px solid #5E5E5E;
    background-color: #3E3E40;
}

QCheckBox::indicator:checked {
    border: 1px solid #0078D7;
    background-color: #0078D7;
}

QRadioButton {
    color: #FFFFFF;
}

QRadioButton::indicator {
    width: 13px;
    height: 13px;
}

QRadioButton::indicator:unchecked {
    border: 1px solid #5E5E5E;
    border-radius: 6px;
    background-color: #3E3E40;
}

QRadioButton::indicator:checked {
    border: 1px solid #0078D7;
    border-radius: 6px;
    background-color: #0078D7;
}
