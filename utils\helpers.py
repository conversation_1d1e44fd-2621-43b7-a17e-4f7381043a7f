#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Helper utilities for Damaged Inventory Management System
"""

import os
import re
from datetime import datetime
from PyQt5.QtWidgets import QMessageBox
from PyQt5.QtCore import QDate

def validate_invoice_no(invoice_no):
    """Validate invoice number format"""
    # Basic validation - can be customized based on requirements
    if not invoice_no or len(invoice_no.strip()) == 0:
        return False, "Invoice number cannot be empty"
    
    # Check if invoice number contains only alphanumeric characters and hyphens
    if not re.match(r'^[A-Za-z0-9\-]+$', invoice_no):
        return False, "Invoice number can only contain letters, numbers, and hyphens"
    
    return True, ""

def validate_quantity(quantity_str):
    """Validate quantity as a positive number"""
    try:
        quantity = float(quantity_str)
        if quantity <= 0:
            return False, "Quantity must be greater than zero"
        return True, quantity
    except ValueError:
        return False, "Quantity must be a valid number"

def validate_customer_name(name):
    """Validate customer name"""
    if not name or len(name.strip()) == 0:
        return False, "Customer name cannot be empty"
    return True, ""

def format_date(date_obj):
    """Format date as YYYY-MM-DD string"""
    if isinstance(date_obj, QDate):
        return date_obj.toString("yyyy-MM-dd")
    elif isinstance(date_obj, datetime):
        return date_obj.strftime("%Y-%m-%d")
    return str(date_obj)

def parse_date(date_str):
    """Parse date string to QDate object"""
    try:
        date = datetime.strptime(date_str, "%Y-%m-%d")
        return QDate(date.year, date.month, date.day)
    except ValueError:
        return QDate.currentDate()

def show_error_message(parent, title, message):
    """Show error message dialog"""
    QMessageBox.critical(parent, title, message)

def show_info_message(parent, title, message):
    """Show information message dialog"""
    QMessageBox.information(parent, title, message)

def show_confirmation_dialog(parent, title, message):
    """Show confirmation dialog and return user's choice"""
    reply = QMessageBox.question(
        parent, title, message,
        QMessageBox.Yes | QMessageBox.No, QMessageBox.No
    )
    return reply == QMessageBox.Yes

def generate_timestamp_filename(prefix, extension):
    """Generate a filename with timestamp"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return f"{prefix}_{timestamp}.{extension}"
