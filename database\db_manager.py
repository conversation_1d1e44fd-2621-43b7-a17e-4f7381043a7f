#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Database manager for Damaged Inventory Management System
"""

import os
import sqlite3
from datetime import datetime
from database.models import ItemMaster, DamagedItem

class DatabaseManager:
    """Manager for SQLite database operations"""
    
    def __init__(self, db_name="damaged_inv.db"):
        """Initialize the database manager"""
        self.db_name = db_name
        self.conn = None
        self.cursor = None
    
    def connect(self):
        """Connect to the SQLite database"""
        try:
            self.conn = sqlite3.connect(self.db_name)
            self.cursor = self.conn.cursor()
            return True
        except sqlite3.Error as e:
            print(f"Database connection error: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from the SQLite database"""
        if self.conn:
            self.conn.close()
            self.conn = None
            self.cursor = None
    
    def setup_database(self):
        """Create tables if they don't exist"""
        if self.connect():
            # Create Item_master table
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS Item_master (
                    Item_code TEXT PRIMARY KEY,
                    Description TEXT NOT NULL,
                    Unit TEXT NOT NULL
                )
            ''')
            
            # Create damaged_item table
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS damaged_item (
                    Invoice_no TEXT PRIMARY KEY,
                    Date TEXT NOT NULL,
                    Item_code TEXT NOT NULL,
                    Item_description TEXT NOT NULL,
                    Unit TEXT NOT NULL,
                    Quantity REAL NOT NULL,
                    Customer_name TEXT NOT NULL,
                    Remarks TEXT,
                    FOREIGN KEY (Item_code) REFERENCES Item_master(Item_code)
                )
            ''')
            
            self.conn.commit()
            self.disconnect()
            return True
        return False
    
    # Item Master CRUD operations
    
    def add_item(self, item):
        """Add a new item to Item_master table"""
        if self.connect():
            try:
                self.cursor.execute(
                    "INSERT INTO Item_master (Item_code, Description, Unit) VALUES (?, ?, ?)",
                    (item.item_code, item.description, item.unit)
                )
                self.conn.commit()
                self.disconnect()
                return True
            except sqlite3.Error as e:
                print(f"Error adding item: {e}")
                self.conn.rollback()
                self.disconnect()
                return False
        return False
    
    def update_item(self, item):
        """Update an existing item in Item_master table"""
        if self.connect():
            try:
                self.cursor.execute(
                    "UPDATE Item_master SET Description = ?, Unit = ? WHERE Item_code = ?",
                    (item.description, item.unit, item.item_code)
                )
                self.conn.commit()
                self.disconnect()
                return True
            except sqlite3.Error as e:
                print(f"Error updating item: {e}")
                self.conn.rollback()
                self.disconnect()
                return False
        return False
    
    def delete_item(self, item_code):
        """Delete an item from Item_master table"""
        if self.connect():
            try:
                self.cursor.execute(
                    "DELETE FROM Item_master WHERE Item_code = ?",
                    (item_code,)
                )
                self.conn.commit()
                self.disconnect()
                return True
            except sqlite3.Error as e:
                print(f"Error deleting item: {e}")
                self.conn.rollback()
                self.disconnect()
                return False
        return False
    
    def get_all_items(self):
        """Get all items from Item_master table"""
        items = []
        if self.connect():
            try:
                self.cursor.execute("SELECT Item_code, Description, Unit FROM Item_master")
                rows = self.cursor.fetchall()
                for row in rows:
                    items.append(ItemMaster.from_db_row(row))
                self.disconnect()
            except sqlite3.Error as e:
                print(f"Error getting items: {e}")
                self.disconnect()
        return items
    
    def get_item(self, item_code):
        """Get a specific item from Item_master table"""
        if self.connect():
            try:
                self.cursor.execute(
                    "SELECT Item_code, Description, Unit FROM Item_master WHERE Item_code = ?",
                    (item_code,)
                )
                row = self.cursor.fetchone()
                self.disconnect()
                if row:
                    return ItemMaster.from_db_row(row)
            except sqlite3.Error as e:
                print(f"Error getting item: {e}")
                self.disconnect()
        return None
    
    # Damaged Item CRUD operations
    
    def add_damaged_item(self, damaged_item):
        """Add a new damaged item to damaged_item table"""
        if self.connect():
            try:
                self.cursor.execute(
                    """INSERT INTO damaged_item 
                       (Invoice_no, Date, Item_code, Item_description, Unit, Quantity, Customer_name, Remarks) 
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?)""",
                    (damaged_item.invoice_no, damaged_item.date, damaged_item.item_code, 
                     damaged_item.item_description, damaged_item.unit, damaged_item.quantity, 
                     damaged_item.customer_name, damaged_item.remarks)
                )
                self.conn.commit()
                self.disconnect()
                return True
            except sqlite3.Error as e:
                print(f"Error adding damaged item: {e}")
                self.conn.rollback()
                self.disconnect()
                return False
        return False
    
    def update_damaged_item(self, damaged_item):
        """Update an existing damaged item in damaged_item table"""
        if self.connect():
            try:
                self.cursor.execute(
                    """UPDATE damaged_item 
                       SET Date = ?, Item_code = ?, Item_description = ?, Unit = ?, 
                           Quantity = ?, Customer_name = ?, Remarks = ? 
                       WHERE Invoice_no = ?""",
                    (damaged_item.date, damaged_item.item_code, damaged_item.item_description, 
                     damaged_item.unit, damaged_item.quantity, damaged_item.customer_name, 
                     damaged_item.remarks, damaged_item.invoice_no)
                )
                self.conn.commit()
                self.disconnect()
                return True
            except sqlite3.Error as e:
                print(f"Error updating damaged item: {e}")
                self.conn.rollback()
                self.disconnect()
                return False
        return False
    
    def delete_damaged_item(self, invoice_no):
        """Delete a damaged item from damaged_item table"""
        if self.connect():
            try:
                self.cursor.execute(
                    "DELETE FROM damaged_item WHERE Invoice_no = ?",
                    (invoice_no,)
                )
                self.conn.commit()
                self.disconnect()
                return True
            except sqlite3.Error as e:
                print(f"Error deleting damaged item: {e}")
                self.conn.rollback()
                self.disconnect()
                return False
        return False
    
    def get_all_damaged_items(self):
        """Get all damaged items from damaged_item table"""
        damaged_items = []
        if self.connect():
            try:
                self.cursor.execute(
                    """SELECT Invoice_no, Date, Item_code, Item_description, Unit, 
                              Quantity, Customer_name, Remarks 
                       FROM damaged_item
                       ORDER BY Invoice_no"""
                )
                rows = self.cursor.fetchall()
                for row in rows:
                    damaged_items.append(DamagedItem.from_db_row(row))
                self.disconnect()
            except sqlite3.Error as e:
                print(f"Error getting damaged items: {e}")
                self.disconnect()
        return damaged_items
    
    def get_damaged_item(self, invoice_no):
        """Get a specific damaged item from damaged_item table"""
        if self.connect():
            try:
                self.cursor.execute(
                    """SELECT Invoice_no, Date, Item_code, Item_description, Unit, 
                              Quantity, Customer_name, Remarks 
                       FROM damaged_item 
                       WHERE Invoice_no = ?""",
                    (invoice_no,)
                )
                row = self.cursor.fetchone()
                self.disconnect()
                if row:
                    return DamagedItem.from_db_row(row)
            except sqlite3.Error as e:
                print(f"Error getting damaged item: {e}")
                self.disconnect()
        return None
