/* Light Mode Style Sheet for Damaged Inventory Management System */

QMainWindow, QDialog, QWidget {
    background-color: #F0F0F0;
    color: #000000;
    font-size: 11pt;
}

QMenuBar {
    background-color: #E0E0E0;
    color: #000000;
}

QMenuBar::item {
    background-color: #E0E0E0;
    color: #000000;
}

QMenuBar::item:selected {
    background-color: #C0C0C0;
}

QMenu {
    background-color: #F0F0F0;
    color: #000000;
    border: 1px solid #C0C0C0;
}

QMenu::item:selected {
    background-color: #C0C0C0;
}

QPushButton {
    background-color: #0078D7;
    color: white;
    border: none;
    padding: 5px 15px;
    border-radius: 3px;
}

QPushButton:hover {
    background-color: #1C97EA;
}

QPushButton:pressed {
    background-color: #00559B;
}

QPushButton:disabled {
    background-color: #C0C0C0;
    color: #808080;
}

QLineEdit, QTextEdit, QComboBox, QDateEdit, QSpinBox, QDoubleSpinBox {
    background-color: #FFFFFF;
    color: #000000;
    border: 1px solid #C0C0C0;
    border-radius: 3px;
    padding: 3px;
}

QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QDateEdit:focus {
    border: 1px solid #0078D7;
}

QComboBox::drop-down {
    border: 0px;
    background-color: #0078D7;
}

QComboBox QAbstractItemView {
    background-color: #FFFFFF;
    color: #000000;
    selection-background-color: #0078D7;
    selection-color: #FFFFFF;
}

QTableView {
    background-color: #FFFFFF;
    color: #000000;
    gridline-color: #C0C0C0;
    selection-background-color: #0078D7;
    selection-color: #FFFFFF;
    alternate-background-color: #F5F5F5;
}

QHeaderView::section {
    background-color: #E0E0E0;
    color: #000000;
    padding: 5px;
    border: 1px solid #C0C0C0;
}

QTabWidget::pane {
    border: 1px solid #C0C0C0;
}

QTabBar::tab {
    background-color: #E0E0E0;
    color: #000000;
    padding: 8px 12px;
    border: 1px solid #C0C0C0;
    border-bottom: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

QTabBar::tab:selected {
    background-color: #0078D7;
    color: #FFFFFF;
}

QTabBar::tab:!selected {
    margin-top: 2px;
}

QLabel {
    color: #000000;
}

QGroupBox {
    border: 1px solid #C0C0C0;
    border-radius: 5px;
    margin-top: 10px;
    font-weight: bold;
    color: #000000;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    left: 10px;
    padding: 0 5px;
}

QStatusBar {
    background-color: #E0E0E0;
    color: #000000;
}

QScrollBar:vertical {
    border: none;
    background-color: #F0F0F0;
    width: 10px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #C0C0C0;
    min-height: 20px;
    border-radius: 5px;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: none;
    background-color: #F0F0F0;
    height: 10px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #C0C0C0;
    min-width: 20px;
    border-radius: 5px;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

QCheckBox {
    color: #000000;
}

QCheckBox::indicator {
    width: 13px;
    height: 13px;
}

QCheckBox::indicator:unchecked {
    border: 1px solid #C0C0C0;
    background-color: #FFFFFF;
}

QCheckBox::indicator:checked {
    border: 1px solid #0078D7;
    background-color: #0078D7;
}

QRadioButton {
    color: #000000;
}

QRadioButton::indicator {
    width: 13px;
    height: 13px;
}

QRadioButton::indicator:unchecked {
    border: 1px solid #C0C0C0;
    border-radius: 6px;
    background-color: #FFFFFF;
}

QRadioButton::indicator:checked {
    border: 1px solid #0078D7;
    border-radius: 6px;
    background-color: #0078D7;
}
