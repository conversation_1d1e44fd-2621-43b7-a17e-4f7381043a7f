#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Login Window for Damaged Inventory Management System
AL FARD ELECTRICAL TRADING LLC
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                            QLineEdit, QPushButton, QMessageBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor
from utils.resource_path import resource_path

class LoginWindow(QDialog):
    """Login window for user authentication"""

    # Signal to emit when login is successful
    login_successful = pyqtSignal()

    def __init__(self):
        super().__init__()

        # Set window properties
        self.setWindowTitle("Login - AL FARD ELECTRICAL TRADING LLC")
        self.setFixedSize(400, 250)
        self.setWindowFlags(Qt.WindowCloseButtonHint | Qt.MSWindowsFixedSizeDialogHint)

        # Set light mode color scheme
        self.set_light_mode()

        # Initialize UI components
        self.init_ui()

        # Set the default credentials (hardcoded for now)
        self.valid_username = "sujeesh"
        self.valid_password = "sujeesh"

    def set_light_mode(self):
        """Set the window to use light mode colors"""
        palette = QPalette()
        palette.setColor(QPalette.Window, QColor(240, 240, 240))
        palette.setColor(QPalette.WindowText, QColor(0, 0, 0))
        palette.setColor(QPalette.Base, QColor(255, 255, 255))
        palette.setColor(QPalette.AlternateBase, QColor(245, 245, 245))
        palette.setColor(QPalette.Text, QColor(0, 0, 0))
        palette.setColor(QPalette.Button, QColor(240, 240, 240))
        palette.setColor(QPalette.ButtonText, QColor(0, 0, 0))
        palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
        palette.setColor(QPalette.HighlightedText, QColor(255, 255, 255))
        self.setPalette(palette)

    def init_ui(self):
        """Initialize the user interface"""
        # Main layout
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)

        # Title label
        title_label = QLabel("Damaged Inventory Management")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # Company label
        company_label = QLabel("AL FARD ELECTRICAL TRADING LLC")
        company_font = QFont()
        company_font.setPointSize(10)
        company_label.setFont(company_font)
        company_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(company_label)

        # Username field
        username_layout = QHBoxLayout()
        username_label = QLabel("Username:")
        username_label.setFixedWidth(80)

        # Set font size for username label (1.25% larger)
        label_font = QFont()
        label_font.setPointSize(9)  # Base size is typically 8, so 9 is more than 1.25% increase
        username_label.setFont(label_font)

        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("Enter your username")

        # Set font size for username input (1.25% larger)
        input_font = QFont()
        input_font.setPointSize(9)  # Base size is typically 8, so 9 is more than 1.25% increase
        self.username_input.setFont(input_font)

        username_layout.addWidget(username_label)
        username_layout.addWidget(self.username_input)
        main_layout.addLayout(username_layout)

        # Password field
        password_layout = QHBoxLayout()
        password_label = QLabel("Password:")
        password_label.setFixedWidth(80)

        # Set font size for password label (1.25% larger)
        password_label.setFont(label_font)

        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("Enter your password")
        self.password_input.setEchoMode(QLineEdit.Password)

        # Set font size for password input (1.25% larger)
        self.password_input.setFont(input_font)

        password_layout.addWidget(password_label)
        password_layout.addWidget(self.password_input)
        main_layout.addLayout(password_layout)

        # Login button
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        self.login_button = QPushButton("Login")
        self.login_button.setFixedWidth(100)
        self.login_button.setFixedHeight(30)  # Increased button height

        # Set font size for login button (1.25% larger)
        button_font = QFont()
        button_font.setPointSize(9)  # Base size is typically 8, so 9 is more than 1.25% increase
        self.login_button.setFont(button_font)

        # Add some padding to the button
        self.login_button.setStyleSheet("padding: 4px;")

        self.login_button.clicked.connect(self.attempt_login)
        button_layout.addWidget(self.login_button)
        button_layout.addStretch()
        main_layout.addLayout(button_layout)

        # Set the main layout
        self.setLayout(main_layout)

        # Connect enter key to login button
        self.username_input.returnPressed.connect(self.login_button.click)
        self.password_input.returnPressed.connect(self.login_button.click)

    def attempt_login(self):
        """Validate the login credentials"""
        username = self.username_input.text()
        password = self.password_input.text()

        if not username or not password:
            QMessageBox.warning(self, "Login Failed", "Please enter both username and password.")
            return

        if username == self.valid_username and password == self.valid_password:
            self.login_successful.emit()
            self.accept()
        else:
            QMessageBox.warning(self, "Login Failed", "Invalid username or password.")
            self.password_input.clear()
            self.password_input.setFocus()
