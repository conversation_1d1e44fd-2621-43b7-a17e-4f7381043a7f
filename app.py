#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Damaged Inventory Management System
AL FARD ELECTRICAL TRADING LLC

Main application entry point
"""

import sys
from PyQt5.QtWidgets import QApplication
from ui.main_window import <PERSON>Window
from ui.login_window import <PERSON>gin<PERSON>indow
from database.db_manager import DatabaseManager

def main():
    """Main function to start the application"""
    # Create the application
    app = QApplication(sys.argv)

    # Initialize the database
    db_manager = DatabaseManager()
    db_manager.setup_database()

    # Create the login window
    login_window = LoginWindow()

    # Create the main window but don't show it yet
    main_window = MainWindow(db_manager)

    # Connect the login_successful signal to show the main window
    def on_login_successful():
        main_window.show()

    login_window.login_successful.connect(on_login_successful)

    # Show the login window
    if login_window.exec_() != LoginWindow.Accepted:
        # User closed the login window without successful login
        return

    # Start the application event loop
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
