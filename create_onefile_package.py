#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Create a distribution package for the Damaged Inventory Management System (Single-file version)
"""

import os
import zipfile
from datetime import datetime

def create_zip_package():
    """Create a ZIP package of the distribution files"""
    # Get the current date for the filename
    current_date = datetime.now().strftime("%Y%m%d")
    
    # Create the ZIP filename
    zip_filename = f"Damaged_Inventory_Management_SingleFile_v1.0.0_{current_date}.zip"
    
    # Create the ZIP file
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        # Add the README file
        zipf.write(os.path.join("dist", "README.txt"), "README.txt")
        
        # Add the batch file
        zipf.write(os.path.join("dist", "Run Damaged Inventory Management.bat"), 
                  "Run Damaged Inventory Management.bat")
        
        # Add the executable
        zipf.write(os.path.join("dist", "Damaged Inventory Management.exe"), 
                  "Damaged Inventory Management.exe")
    
    print(f"Distribution package created: {zip_filename}")
    return zip_filename

if __name__ == "__main__":
    create_zip_package()
