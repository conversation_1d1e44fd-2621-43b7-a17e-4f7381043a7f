#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Item Master Form for Damaged Inventory Management System
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLabel, QLineEdit, QPushButton, QTableView,
                            QHeaderView, QMessageBox, QGroupBox)
from PyQt5.QtCore import Qt, QAbstractTableModel, QModelIndex
from PyQt5.QtGui import QFont
from database.models import ItemMaster
from utils.helpers import show_error_message, show_info_message, show_confirmation_dialog

class ItemTableModel(QAbstractTableModel):
    """Table model for displaying items"""

    def __init__(self, items=None):
        super().__init__()
        self.items = items or []
        self.headers = ["Item Code", "Description", "Unit"]

    def rowCount(self, parent=QModelIndex()):
        return len(self.items)

    def columnCount(self, parent=QModelIndex()):
        return len(self.headers)

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid() or not (0 <= index.row() < len(self.items)):
            return None

        item = self.items[index.row()]
        col = index.column()

        if role == Qt.DisplayRole:
            if col == 0:
                return item.item_code
            elif col == 1:
                return item.description
            elif col == 2:
                return item.unit

        return None

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if role == Qt.DisplayRole and orientation == Qt.Horizontal:
            return self.headers[section]
        return None

    def update_items(self, items):
        """Update the model with new items"""
        self.beginResetModel()
        self.items = items
        self.endResetModel()


class ItemMasterForm(QDialog):
    """Form for managing items in the Item_master table"""

    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.selected_item = None

        self.setWindowTitle("Item Master")
        self.setMinimumSize(800, 600)

        self.init_ui()
        self.load_items()

    def init_ui(self):
        """Initialize the UI components"""
        main_layout = QVBoxLayout()

        # Form group
        form_group = QGroupBox("Item Details")
        form_layout = QFormLayout()

        # Item code field
        self.item_code_edit = QLineEdit()
        form_layout.addRow("Item Code:", self.item_code_edit)

        # Description field
        self.description_edit = QLineEdit()
        form_layout.addRow("Description:", self.description_edit)

        # Unit field
        self.unit_edit = QLineEdit()
        form_layout.addRow("Unit:", self.unit_edit)

        form_group.setLayout(form_layout)
        main_layout.addWidget(form_group)

        # Buttons layout
        buttons_layout = QHBoxLayout()

        # Add button
        self.add_button = QPushButton("Add")
        self.add_button.clicked.connect(self.add_item)
        buttons_layout.addWidget(self.add_button)

        # Update button
        self.update_button = QPushButton("Update")
        self.update_button.clicked.connect(self.update_item)
        self.update_button.setEnabled(False)
        buttons_layout.addWidget(self.update_button)

        # Delete button
        self.delete_button = QPushButton("Delete")
        self.delete_button.clicked.connect(self.delete_item)
        self.delete_button.setEnabled(False)
        buttons_layout.addWidget(self.delete_button)

        # Clear button
        self.clear_button = QPushButton("Clear")
        self.clear_button.clicked.connect(self.clear_form)
        buttons_layout.addWidget(self.clear_button)

        main_layout.addLayout(buttons_layout)

        # Table view
        self.table_model = ItemTableModel()
        self.table_view = QTableView()
        self.table_view.setModel(self.table_model)
        self.table_view.setSelectionBehavior(QTableView.SelectRows)
        self.table_view.setSelectionMode(QTableView.SingleSelection)
        self.table_view.setAlternatingRowColors(True)
        self.table_view.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.table_view.clicked.connect(self.on_table_clicked)
        self.table_view.doubleClicked.connect(self.on_table_double_clicked)

        main_layout.addWidget(self.table_view)

        # Close button
        close_layout = QHBoxLayout()
        close_layout.addStretch()

        self.close_button = QPushButton("Close")
        self.close_button.clicked.connect(self.close)
        close_layout.addWidget(self.close_button)

        main_layout.addLayout(close_layout)

        self.setLayout(main_layout)

    def load_items(self):
        """Load items from the database"""
        items = self.db_manager.get_all_items()
        self.table_model.update_items(items)

    def clear_form(self):
        """Clear the form fields"""
        self.item_code_edit.clear()
        self.description_edit.clear()
        self.unit_edit.clear()
        self.item_code_edit.setEnabled(True)
        self.selected_item = None
        self.update_button.setEnabled(False)
        self.delete_button.setEnabled(False)
        self.add_button.setEnabled(True)

    def on_table_clicked(self, index):
        """Handle table row selection"""
        row = index.row()
        if row < 0 or row >= len(self.table_model.items):
            return

        self.selected_item = self.table_model.items[row]

        self.item_code_edit.setText(self.selected_item.item_code)
        self.description_edit.setText(self.selected_item.description)
        self.unit_edit.setText(self.selected_item.unit)

        self.item_code_edit.setEnabled(False)
        # Don't enable update/delete buttons on single click
        self.add_button.setEnabled(False)

    def on_table_double_clicked(self, index):
        """Handle double-click on table row"""
        self.on_table_clicked(index)
        if self.selected_item:
            # Enable update/delete buttons only on double-click
            self.update_button.setEnabled(True)
            self.delete_button.setEnabled(True)

    def validate_form(self):
        """Validate form inputs"""
        item_code = self.item_code_edit.text().strip()
        description = self.description_edit.text().strip()
        unit = self.unit_edit.text().strip()

        if not item_code:
            show_error_message(self, "Validation Error", "Item code cannot be empty")
            return False

        if not description:
            show_error_message(self, "Validation Error", "Description cannot be empty")
            return False

        if not unit:
            show_error_message(self, "Validation Error", "Unit cannot be empty")
            return False

        return True

    def add_item(self):
        """Add a new item to the database"""
        if not self.validate_form():
            return

        item_code = self.item_code_edit.text().strip()
        description = self.description_edit.text().strip()
        unit = self.unit_edit.text().strip()

        # Check if item code already exists
        existing_item = self.db_manager.get_item(item_code)
        if existing_item:
            show_error_message(self, "Error", f"Item code '{item_code}' already exists")
            return

        # Create new item
        new_item = ItemMaster(item_code, description, unit)

        # Add to database
        if self.db_manager.add_item(new_item):
            show_info_message(self, "Success", "Item added successfully")
            self.clear_form()
            self.load_items()
        else:
            show_error_message(self, "Error", "Failed to add item")

    def update_item(self):
        """Update an existing item in the database"""
        if not self.validate_form() or not self.selected_item:
            return

        # Confirm update
        if not show_confirmation_dialog(
            self, "Confirm Update",
            f"Are you sure you want to update item '{self.selected_item.item_code}'?"
        ):
            return

        description = self.description_edit.text().strip()
        unit = self.unit_edit.text().strip()

        # Update item
        self.selected_item.description = description
        self.selected_item.unit = unit

        # Store item code before clearing the form
        item_code = self.selected_item.item_code

        # Update in database
        if self.db_manager.update_item(self.selected_item):
            show_info_message(self, "Success", "Item updated successfully")
            self.clear_form()
            self.load_items()
        else:
            show_error_message(self, "Error", "Failed to update item")

    def delete_item(self):
        """Delete an item from the database"""
        if not self.selected_item:
            return

        # Confirm deletion
        if not show_confirmation_dialog(
            self, "Confirm Deletion",
            f"Are you sure you want to delete item '{self.selected_item.item_code}'?"
        ):
            return

        # Store item code before clearing the form
        item_code = self.selected_item.item_code

        # Delete from database
        if self.db_manager.delete_item(item_code):
            show_info_message(self, "Success", "Item deleted successfully")
            self.clear_form()
            self.load_items()
        else:
            show_error_message(self, "Error", "Failed to delete item")
