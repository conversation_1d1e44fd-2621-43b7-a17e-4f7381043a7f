#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Report Generator for Damaged Inventory Management System
"""

import os
from datetime import datetime
from PyQt5.QtWidgets import QFileDialog, QMessageBox
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, mm
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.platypus.flowables import KeepTogether
from utils.helpers import generate_timestamp_filename

class ReportGenerator:
    """Generate PDF reports for damaged items"""

    def __init__(self, db_manager, parent=None):
        self.db_manager = db_manager
        self.parent = parent

    def generate_damaged_items_report(self, save_path=None):
        """Generate a report of all damaged items"""
        # Get all damaged items
        damaged_items = self.db_manager.get_all_damaged_items()

        if not damaged_items:
            QMessageBox.warning(self.parent, "No Data", "No damaged items found to generate report.")
            return False

        # If no save path provided, ask user for location
        if not save_path:
            default_filename = generate_timestamp_filename("damaged_items_report", "pdf")
            save_path, _ = QFileDialog.getSaveFileName(
                self.parent, "Save Report", default_filename, "PDF Files (*.pdf)"
            )

            if not save_path:
                return False  # User cancelled

        try:
            # Create the PDF document
            doc = SimpleDocTemplate(
                save_path,
                pagesize=A4,
                rightMargin=20*mm,
                leftMargin=20*mm,
                topMargin=20*mm,
                bottomMargin=20*mm
            )

            # Styles
            styles = getSampleStyleSheet()
            title_style = styles["Heading1"]
            title_style.alignment = 1  # Center alignment

            subtitle_style = styles["Heading2"]
            subtitle_style.alignment = 1

            normal_style = styles["Normal"]

            # Create a style for table cells with word wrapping
            cell_style = ParagraphStyle(
                'CellStyle',
                parent=styles['Normal'],
                fontSize=8,
                leading=10,
                wordWrap='CJK',
                alignment=0  # Left alignment
            )

            # Content elements
            elements = []

            # Title
            elements.append(Paragraph("AL FARD ELECTRICAL TRADING LLC - AL AIN DAMAGED ITEM REPORT", title_style))
            elements.append(Spacer(1, 10*mm))

            # Date
            date_text = f"Report Date: {datetime.now().strftime('%Y-%m-%d')}"
            elements.append(Paragraph(date_text, normal_style))
            elements.append(Spacer(1, 10*mm))

            # Table data with column headers
            headers = ["Invoice No", "Date", "Item Code", "Description", "Unit", "Quantity", "Customer", "Remarks"]
            data = [headers]

            # Function to wrap text in paragraphs
            def wrap_text(text):
                if text and len(str(text)) > 20:
                    return Paragraph(str(text), cell_style)
                return text

            # Add data rows with wrapped text
            for item in damaged_items:
                row = [
                    wrap_text(item.invoice_no),
                    wrap_text(item.date),
                    wrap_text(item.item_code),
                    wrap_text(item.item_description),
                    wrap_text(item.unit),
                    str(item.quantity),  # Quantity usually doesn't need wrapping
                    wrap_text(item.customer_name),
                    wrap_text(item.remarks or "")
                ]
                data.append(row)

            # Create table with appropriate column widths
            col_widths = [60, 60, 60, 100, 40, 40, 80, 100]  # Adjust as needed
            table = Table(data, repeatRows=1, colWidths=col_widths)

            # Style the table
            table_style = TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
                ('ALIGN', (0, 1), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 1), (-1, -1), 8),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),  # Changed to TOP for better text wrapping
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                ('BOX', (0, 0), (-1, -1), 0.5, colors.black),
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
                ('LEFTPADDING', (0, 0), (-1, -1), 3),
                ('RIGHTPADDING', (0, 0), (-1, -1), 3),
                ('TOPPADDING', (0, 0), (-1, -1), 3),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 3)
            ])

            table.setStyle(table_style)
            # Wrap the table in KeepTogether to prevent it from breaking across pages if possible
            elements.append(KeepTogether(table))

            # Build the PDF
            doc.build(elements)

            return True

        except Exception as e:
            QMessageBox.critical(self.parent, "Error", f"Failed to generate report: {str(e)}")
            return False
