#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Database models for Damaged Inventory Management System
"""

class ItemMaster:
    """Model for Item_master table"""
    def __init__(self, item_code=None, description=None, unit=None):
        self.item_code = item_code
        self.description = description
        self.unit = unit
    
    def __str__(self):
        return f"{self.item_code} - {self.description}"
    
    @classmethod
    def from_db_row(cls, row):
        """Create an ItemMaster object from a database row"""
        return cls(
            item_code=row[0],
            description=row[1],
            unit=row[2]
        )


class DamagedItem:
    """Model for damaged_item table"""
    def __init__(self, invoice_no=None, date=None, item_code=None, 
                 item_description=None, unit=None, quantity=None, 
                 customer_name=None, remarks=None):
        self.invoice_no = invoice_no
        self.date = date
        self.item_code = item_code
        self.item_description = item_description
        self.unit = unit
        self.quantity = quantity
        self.customer_name = customer_name
        self.remarks = remarks
    
    def __str__(self):
        return f"Invoice #{self.invoice_no} - {self.item_description}"
    
    @classmethod
    def from_db_row(cls, row):
        """Create a DamagedItem object from a database row"""
        return cls(
            invoice_no=row[0],
            date=row[1],
            item_code=row[2],
            item_description=row[3],
            unit=row[4],
            quantity=row[5],
            customer_name=row[6],
            remarks=row[7]
        )
