#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Main Window for Damaged Inventory Management System
"""

import os
import sys
from datetime import datetime
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QFormLayout, QLabel, QLineEdit, QComboBox,
                            QDateEdit, QDoubleSpinBox, QTextEdit, QPushButton,
                            QTableView, QHeaderView, QAction, QMenu, QToolBar,
                            QStatusBar, QMessageBox, QGroupBox, QSplitter,
                            QDialog, QFileDialog, QAbstractItemView)
from PyQt5.QtCore import Qt, QDate, QAbstractTableModel, QModelIndex, QSortFilterProxyModel
from PyQt5.QtGui import QIcon, QFont
from database.models import DamagedItem
from ui.item_master_form import ItemMasterForm
from ui.report_generator import ReportGenerator
from utils.helpers import (validate_invoice_no, validate_quantity, validate_customer_name,
                          format_date, parse_date, show_error_message,
                          show_info_message, show_confirmation_dialog)
from utils.resource_path import resource_path

class DamagedItemTableModel(QAbstractTableModel):
    """Table model for displaying damaged items"""

    def __init__(self, items=None):
        super().__init__()
        self.items = items or []
        self.headers = ["Invoice No", "Date", "Item Code", "Description", "Unit", "Quantity", "Customer", "Remarks"]

    def rowCount(self, parent=QModelIndex()):
        return len(self.items)

    def columnCount(self, parent=QModelIndex()):
        return len(self.headers)

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid() or not (0 <= index.row() < len(self.items)):
            return None

        item = self.items[index.row()]
        col = index.column()

        if role == Qt.DisplayRole:
            if col == 0:
                return item.invoice_no
            elif col == 1:
                return item.date
            elif col == 2:
                return item.item_code
            elif col == 3:
                return item.item_description
            elif col == 4:
                return item.unit
            elif col == 5:
                return str(item.quantity)
            elif col == 6:
                return item.customer_name
            elif col == 7:
                return item.remarks

        return None

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if role == Qt.DisplayRole and orientation == Qt.Horizontal:
            return self.headers[section]
        return None

    def update_items(self, items):
        """Update the model with new items"""
        self.beginResetModel()
        self.items = items
        self.endResetModel()


class AboutDialog(QDialog):
    """About dialog for the application"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("About")
        self.setFixedSize(400, 300)

        layout = QVBoxLayout()

        # Application name
        app_name = QLabel("Damaged Inventory Management System")
        app_name.setAlignment(Qt.AlignCenter)
        font = QFont()
        font.setPointSize(14)
        font.setBold(True)
        app_name.setFont(font)
        layout.addWidget(app_name)

        # Company name
        company_name = QLabel("AL FARD ELECTRICAL TRADING LLC - AL AIN")
        company_name.setAlignment(Qt.AlignCenter)
        font = QFont()
        font.setPointSize(12)
        company_name.setFont(font)
        layout.addWidget(company_name)

        layout.addSpacing(20)

        # Version
        version = QLabel("Version 1.0.0")
        version.setAlignment(Qt.AlignCenter)
        layout.addWidget(version)

        layout.addSpacing(20)

        # Description
        description = QLabel(
            "A modern inventory management system for tracking damaged items.\n\n"
            "This application is developed by Akbar KP.\n"
            "email: <EMAIL>"
        )
        description.setAlignment(Qt.AlignCenter)
        description.setWordWrap(True)
        layout.addWidget(description)

        layout.addStretch()

        # Close button
        close_button = QPushButton("Close")
        close_button.clicked.connect(self.close)
        layout.addWidget(close_button)

        self.setLayout(layout)


class MainWindow(QMainWindow):
    """Main window for the Damaged Inventory Management System"""

    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.selected_item = None
        self.dark_mode = False  # Start with light mode by default

        self.setWindowTitle("Damaged Inventory Management System - AL FARD ELECTRICAL TRADING LLC - AL AIN")
        self.setMinimumSize(1200, 800)

        self.init_ui()
        self.load_items()
        self.load_item_codes()
        self.apply_stylesheet()

    def init_ui(self):
        """Initialize the UI components"""
        # Central widget
        central_widget = QWidget()
        main_layout = QVBoxLayout()

        # Create form group
        form_group = QGroupBox("Damaged Item Details")
        form_layout = QFormLayout()

        # Invoice No field
        self.invoice_no_edit = QLineEdit()
        form_layout.addRow("Invoice No:", self.invoice_no_edit)

        # Date field
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())
        form_layout.addRow("Date:", self.date_edit)

        # Item Code field
        self.item_code_combo = QComboBox()
        self.item_code_combo.setEditable(True)
        self.item_code_combo.currentIndexChanged.connect(self.on_item_selected)
        form_layout.addRow("Item Code:", self.item_code_combo)

        # Item Description field
        self.item_description_edit = QLineEdit()
        self.item_description_edit.setReadOnly(True)
        form_layout.addRow("Item Description:", self.item_description_edit)

        # Unit field
        self.unit_edit = QLineEdit()
        self.unit_edit.setReadOnly(True)
        form_layout.addRow("Unit:", self.unit_edit)

        # Quantity field
        self.quantity_spin = QDoubleSpinBox()
        self.quantity_spin.setMinimum(0.01)
        self.quantity_spin.setMaximum(9999.99)
        self.quantity_spin.setValue(1.0)
        form_layout.addRow("Quantity:", self.quantity_spin)

        # Customer Name field
        self.customer_name_edit = QLineEdit()
        form_layout.addRow("Customer Name:", self.customer_name_edit)

        # Remarks field
        self.remarks_edit = QTextEdit()
        self.remarks_edit.setMaximumHeight(100)
        form_layout.addRow("Remarks:", self.remarks_edit)

        form_group.setLayout(form_layout)
        main_layout.addWidget(form_group)

        # Buttons layout
        buttons_layout = QHBoxLayout()

        # Add button
        self.add_button = QPushButton("Add")
        self.add_button.clicked.connect(self.add_damaged_item)
        buttons_layout.addWidget(self.add_button)

        # Update button
        self.update_button = QPushButton("Update")
        self.update_button.clicked.connect(self.update_damaged_item)
        self.update_button.setEnabled(False)
        buttons_layout.addWidget(self.update_button)

        # Delete button
        self.delete_button = QPushButton("Delete")
        self.delete_button.clicked.connect(self.delete_damaged_item)
        self.delete_button.setEnabled(False)
        buttons_layout.addWidget(self.delete_button)

        # Clear button
        self.clear_button = QPushButton("Clear")
        self.clear_button.clicked.connect(self.clear_form)
        buttons_layout.addWidget(self.clear_button)

        main_layout.addLayout(buttons_layout)

        # Search layout
        search_layout = QHBoxLayout()
        search_label = QLabel("Search by Invoice No:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Enter invoice number to search...")
        self.search_edit.textChanged.connect(self.filter_table)
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_edit)

        # Clear search button
        self.clear_search_button = QPushButton("Clear Search")
        self.clear_search_button.clicked.connect(self.clear_search)
        search_layout.addWidget(self.clear_search_button)

        main_layout.addLayout(search_layout)

        # Table view
        self.table_model = DamagedItemTableModel()
        self.proxy_model = QSortFilterProxyModel()
        self.proxy_model.setSourceModel(self.table_model)

        self.table_view = QTableView()
        self.table_view.setModel(self.proxy_model)
        self.table_view.setSortingEnabled(True)
        self.table_view.setSelectionBehavior(QTableView.SelectRows)
        self.table_view.setSelectionMode(QTableView.SingleSelection)
        self.table_view.setAlternatingRowColors(True)
        self.table_view.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.table_view.clicked.connect(self.on_table_clicked)
        self.table_view.doubleClicked.connect(self.on_table_double_clicked)

        main_layout.addWidget(self.table_view)

        central_widget.setLayout(main_layout)
        self.setCentralWidget(central_widget)

        # Create menu bar
        self.create_menu_bar()

        # Create status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")

    def create_menu_bar(self):
        """Create the menu bar"""
        menu_bar = self.menuBar()

        # File menu
        file_menu = menu_bar.addMenu("File")

        exit_action = QAction("Exit", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Edit menu
        edit_menu = menu_bar.addMenu("Edit")

        add_action = QAction("Add Item", self)
        add_action.triggered.connect(self.add_damaged_item)
        edit_menu.addAction(add_action)

        update_action = QAction("Update Item", self)
        update_action.triggered.connect(self.update_damaged_item)
        edit_menu.addAction(update_action)

        delete_action = QAction("Delete Item", self)
        delete_action.triggered.connect(self.delete_damaged_item)
        edit_menu.addAction(delete_action)

        edit_menu.addSeparator()

        clear_action = QAction("Clear Form", self)
        clear_action.triggered.connect(self.clear_form)
        edit_menu.addAction(clear_action)

        # Settings menu
        settings_menu = menu_bar.addMenu("Settings")

        item_master_action = QAction("Item Master", self)
        item_master_action.triggered.connect(self.open_item_master)
        settings_menu.addAction(item_master_action)

        settings_menu.addSeparator()

        theme_action = QAction("Toggle Dark/Light Mode", self)
        theme_action.triggered.connect(self.toggle_theme)
        settings_menu.addAction(theme_action)

        # Report menu
        report_menu = menu_bar.addMenu("Report")

        damaged_report_action = QAction("Damaged Return Report", self)
        damaged_report_action.triggered.connect(self.generate_report)
        report_menu.addAction(damaged_report_action)

        # About menu
        about_menu = menu_bar.addMenu("About")

        about_action = QAction("About", self)
        about_action.triggered.connect(self.show_about_dialog)
        about_menu.addAction(about_action)

    def apply_stylesheet(self):
        """Apply the appropriate stylesheet based on the current theme"""
        if self.dark_mode:
            style_path = resource_path("ui/resources/styles/dark_style.qss")
        else:
            style_path = resource_path("ui/resources/styles/light_style.qss")

        try:
            with open(style_path, "r") as f:
                self.setStyleSheet(f.read())
        except Exception as e:
            print(f"Error loading stylesheet: {e}")
            # If there's an error, try a fallback approach
            try:
                # Try looking in the current directory
                fallback_path = os.path.join(os.path.dirname(os.path.abspath(__file__)),
                                           "resources", "styles",
                                           "dark_style.qss" if self.dark_mode else "light_style.qss")
                with open(fallback_path, "r") as f:
                    self.setStyleSheet(f.read())
            except Exception as e2:
                print(f"Fallback also failed: {e2}")

    def toggle_theme(self):
        """Toggle between dark and light mode"""
        self.dark_mode = not self.dark_mode
        self.apply_stylesheet()

    def load_items(self):
        """Load damaged items from the database"""
        items = self.db_manager.get_all_damaged_items()
        self.table_model.update_items(items)
        # Apply current filter if search box has text
        if hasattr(self, 'search_edit') and self.search_edit.text():
            self.filter_table(self.search_edit.text())

    def load_item_codes(self):
        """Load item codes from the database"""
        items = self.db_manager.get_all_items()
        self.item_code_combo.clear()
        self.item_code_combo.addItem("", None)  # Empty item
        for item in items:
            self.item_code_combo.addItem(item.item_code, item)

    def on_item_selected(self, index):
        """Handle item selection from the combo box"""
        if index <= 0:  # Empty item or no selection
            self.item_description_edit.clear()
            self.unit_edit.clear()
            return

        item = self.item_code_combo.itemData(index)
        if item:
            self.item_description_edit.setText(item.description)
            self.unit_edit.setText(item.unit)

    def clear_form(self):
        """Clear the form fields"""
        self.invoice_no_edit.clear()
        self.date_edit.setDate(QDate.currentDate())
        self.item_code_combo.setCurrentIndex(0)
        self.item_description_edit.clear()
        self.unit_edit.clear()
        self.quantity_spin.setValue(1.0)
        self.customer_name_edit.clear()
        self.remarks_edit.clear()

        self.invoice_no_edit.setEnabled(True)
        self.selected_item = None
        self.update_button.setEnabled(False)
        self.delete_button.setEnabled(False)
        self.add_button.setEnabled(True)

    def on_table_clicked(self, index):
        """Handle table row selection"""
        row = self.proxy_model.mapToSource(index).row()
        if row < 0 or row >= len(self.table_model.items):
            return

        self.selected_item = self.table_model.items[row]

        self.invoice_no_edit.setText(self.selected_item.invoice_no)
        self.invoice_no_edit.setEnabled(False)

        self.date_edit.setDate(parse_date(self.selected_item.date))

        # Find and select the item code in the combo box
        index = self.item_code_combo.findText(self.selected_item.item_code)
        if index >= 0:
            self.item_code_combo.setCurrentIndex(index)

        self.item_description_edit.setText(self.selected_item.item_description)
        self.unit_edit.setText(self.selected_item.unit)
        self.quantity_spin.setValue(float(self.selected_item.quantity))
        self.customer_name_edit.setText(self.selected_item.customer_name)
        self.remarks_edit.setText(self.selected_item.remarks or "")

        # Don't enable update/delete buttons on single click
        self.add_button.setEnabled(False)

    def on_table_double_clicked(self, index):
        """Handle double-click on table row"""
        self.on_table_clicked(index)
        if self.selected_item:
            # Enable update/delete buttons only on double-click
            self.update_button.setEnabled(True)
            self.delete_button.setEnabled(True)

    def validate_form(self):
        """Validate form inputs"""
        invoice_no = self.invoice_no_edit.text().strip()
        valid, message = validate_invoice_no(invoice_no)
        if not valid:
            show_error_message(self, "Validation Error", message)
            return False

        item_code = self.item_code_combo.currentText().strip()
        if not item_code:
            show_error_message(self, "Validation Error", "Please select an item code")
            return False

        customer_name = self.customer_name_edit.text().strip()
        valid, message = validate_customer_name(customer_name)
        if not valid:
            show_error_message(self, "Validation Error", message)
            return False

        return True

    def add_damaged_item(self):
        """Add a new damaged item to the database"""
        if not self.validate_form():
            return

        invoice_no = self.invoice_no_edit.text().strip()
        date = format_date(self.date_edit.date())
        item_code = self.item_code_combo.currentText().strip()
        item_description = self.item_description_edit.text().strip()
        unit = self.unit_edit.text().strip()
        quantity = self.quantity_spin.value()
        customer_name = self.customer_name_edit.text().strip()
        remarks = self.remarks_edit.toPlainText().strip()

        # Check if invoice number already exists
        existing_item = self.db_manager.get_damaged_item(invoice_no)
        if existing_item:
            show_error_message(self, "Error", f"Invoice number '{invoice_no}' already exists")
            return

        # Create new damaged item
        new_item = DamagedItem(
            invoice_no=invoice_no,
            date=date,
            item_code=item_code,
            item_description=item_description,
            unit=unit,
            quantity=quantity,
            customer_name=customer_name,
            remarks=remarks
        )

        # Add to database
        if self.db_manager.add_damaged_item(new_item):
            show_info_message(self, "Success", "Damaged item added successfully")
            self.clear_form()
            self.load_items()
            self.status_bar.showMessage(f"Added damaged item with invoice #{invoice_no}")
        else:
            show_error_message(self, "Error", "Failed to add damaged item")

    def update_damaged_item(self):
        """Update an existing damaged item in the database"""
        if not self.validate_form() or not self.selected_item:
            return

        # Confirm update
        if not show_confirmation_dialog(
            self, "Confirm Update",
            f"Are you sure you want to update damaged item with invoice #{self.selected_item.invoice_no}?"
        ):
            return

        date = format_date(self.date_edit.date())
        item_code = self.item_code_combo.currentText().strip()
        item_description = self.item_description_edit.text().strip()
        unit = self.unit_edit.text().strip()
        quantity = self.quantity_spin.value()
        customer_name = self.customer_name_edit.text().strip()
        remarks = self.remarks_edit.toPlainText().strip()

        # Update item
        self.selected_item.date = date
        self.selected_item.item_code = item_code
        self.selected_item.item_description = item_description
        self.selected_item.unit = unit
        self.selected_item.quantity = quantity
        self.selected_item.customer_name = customer_name
        self.selected_item.remarks = remarks

        # Store invoice number before clearing the form
        invoice_no = self.selected_item.invoice_no

        # Update in database
        if self.db_manager.update_damaged_item(self.selected_item):
            show_info_message(self, "Success", "Damaged item updated successfully")
            self.clear_form()
            self.load_items()
            self.status_bar.showMessage(f"Updated damaged item with invoice #{invoice_no}")
        else:
            show_error_message(self, "Error", "Failed to update damaged item")

    def delete_damaged_item(self):
        """Delete a damaged item from the database"""
        if not self.selected_item:
            return

        # Confirm deletion
        if not show_confirmation_dialog(
            self, "Confirm Deletion",
            f"Are you sure you want to delete damaged item with invoice #{self.selected_item.invoice_no}?"
        ):
            return

        # Store invoice number before clearing the form
        invoice_no = self.selected_item.invoice_no

        # Delete from database
        if self.db_manager.delete_damaged_item(invoice_no):
            show_info_message(self, "Success", "Damaged item deleted successfully")
            self.clear_form()
            self.load_items()
            self.status_bar.showMessage(f"Deleted damaged item with invoice #{invoice_no}")
        else:
            show_error_message(self, "Error", "Failed to delete damaged item")

    def open_item_master(self):
        """Open the Item Master form"""
        item_master_form = ItemMasterForm(self.db_manager, self)
        item_master_form.exec_()

        # Reload item codes after the form is closed
        self.load_item_codes()

    def generate_report(self):
        """Generate a damaged items report"""
        report_generator = ReportGenerator(self.db_manager, self)
        if report_generator.generate_damaged_items_report():
            show_info_message(self, "Success", "Report generated successfully")
            self.status_bar.showMessage("Report generated successfully")

    def show_about_dialog(self):
        """Show the About dialog"""
        about_dialog = AboutDialog(self)
        about_dialog.exec_()

    def filter_table(self, text):
        """Filter the table by invoice number"""
        # Set filter to case insensitive
        self.proxy_model.setFilterCaseSensitivity(Qt.CaseInsensitive)
        # Set filter to first column (Invoice No)
        self.proxy_model.setFilterKeyColumn(0)
        # Apply the filter
        self.proxy_model.setFilterFixedString(text)

        # Update status bar with filter info
        if text:
            count = self.proxy_model.rowCount()
            self.status_bar.showMessage(f"Found {count} item(s) matching '{text}'")
        else:
            self.status_bar.showMessage("Ready")

    def clear_search(self):
        """Clear the search box and reset the filter"""
        self.search_edit.clear()
        self.status_bar.showMessage("Ready")
